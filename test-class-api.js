// 测试班级API修复
// 这个文件用于验证班级API的URL修复是否正确

import { getClassesList, getClassById } from './src/api/class.js';

// 测试获取班级列表的URL构建
console.log('测试班级API URL修复');

// 模拟测试 - 检查URL构建
const testDeptId = 101;

// 这应该生成正确的URL: /system/dept/list/second/101
// 而不是错误的URL: /system/dept/list/second101

console.log('修复前的错误URL: /system/dept/list/second' + testDeptId); // /system/dept/list/second101
console.log('修复后的正确URL: /system/dept/list/second/' + testDeptId); // /system/dept/list/second/101

// 测试getClassById的URL构建
const testClassId = 123;
console.log('修复前的错误URL: /system/dept/list/include/{deptId}'); // 模板字符串未替换
console.log('修复后的正确URL: /system/dept/list/include/' + testClassId); // /system/dept/list/include/123

console.log('URL修复完成！');
